import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import '../common/constants/colors.dart';
import '../core/utils.dart';
import '../providers/cart_provider.dart';
import '../providers/data_provider.dart';

class CartBottomBar extends StatelessWidget {
  final Color? buttonColor;
  final TextStyle? textStyle;

  const CartBottomBar({ this.buttonColor, this.textStyle, super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer2<CartProvider, DataProvider>(
      builder: (context, cartProvider, dataProvider, _) {
        return AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          height: cartProvider.isCartEmpty ? 0 : 150,
          child: Container(
            margin: const EdgeInsets.all(5),
            decoration: BoxDecoration(
              gradient: AppColors.primaryGradient,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [BoxShadow(color: AppColors.buttonPrimary.withValues(alpha: 0.3), offset: const Offset(0, 8), blurRadius: 20)],
            ),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(color: AppColors.overlayLight, borderRadius: BorderRadius.circular(12)),
                    child: const Icon(Icons.shopping_cart, color: AppColors.textOnDark, size: 40),
                  ),
                  const SizedBox(width: 10),

                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Flexible(
                          child: Text(
                            '${cartProvider.totalItems} items in cart',
                            style: const TextStyle(color: Colors.white, fontSize: 28, fontWeight: FontWeight.w600),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                            softWrap: false,
                          ),
                        ),
                        if (!cartProvider.isEmpty && cartProvider.totalPrice > 0)
                          Flexible(
                            child: Text(
                              formatCurrencyWithRestaurant(
                                cartProvider.total,
                                dataProvider.hasRestaurant ? dataProvider.restaurant.restaurantSettings.currencyValue : null
                              ),
                              style: const TextStyle(color: Colors.white, fontSize: 24, fontWeight: FontWeight.w400),
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                              softWrap: false,
                            ),
                          ),
                      ],
                    ),
                  ),

                  ElevatedButton(
                    onPressed: () {
                      context.push('/cart');
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.white,
                      foregroundColor: AppColors.primary,
                      elevation: 0,
                      padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
                    ),
                    child: const Text('Checkout', style: TextStyle(fontSize: 24, fontWeight: FontWeight.w700)),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
