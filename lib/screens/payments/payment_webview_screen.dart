import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:go_router/go_router.dart';

import '../../common/constants/colors.dart';
import '../../models/order/order.dart';
import '../../models/payment_request_details.dart';
import '../../models/restaurant/restaurant.dart';
import '../../services/payment_gateway_service.dart';
import '../../widgets/loading_dialog.dart';

class PaymentWebViewScreen extends StatefulWidget {
  final PaymentRequestDetails paymentDetails;
  final Restaurant restaurant;
  final Order order;

  const PaymentWebViewScreen({
    super.key,
    required this.paymentDetails,
    required this.restaurant,
    required this.order,
  });

  @override
  State<PaymentWebViewScreen> createState() => _PaymentWebViewScreenState();
}

class _PaymentWebViewScreenState extends State<PaymentWebViewScreen> {
  InAppWebViewController? webViewController;
  bool isLoading = true;
  String? errorMessage;
  late PaymentGatewayService paymentService;
  LoadingDialog? loadingDialog;
  bool _hasLoadingStarted = false;
  bool _hasLoadingCompleted = false;

  @override
  void initState() {
    super.initState();
    paymentService = PaymentGatewayService(context);

    // Debug payment details
    debugPrint('PaymentWebView: Payment gateway object: ${widget.paymentDetails.paymentGatewayObj}');
    debugPrint('PaymentWebView: Payment URL: ${widget.paymentDetails.paymentGatewayObj['url']}');

    // Show immediate loading dialog when screen opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        loadingDialog = LoadingDialog(context, 'Preparing payment form...');
        loadingDialog?.show();

        // Add timeout to dismiss loading dialog if WebView doesn't load within reasonable time
        _startLoadingTimeout();
      }
    });
  }

  void _startLoadingTimeout() {
    // Set a timeout to dismiss loading dialog if WebView takes too long
    Future.delayed(const Duration(seconds: 15), () {
      if (mounted && !_hasLoadingCompleted && loadingDialog != null) {
        debugPrint('PaymentWebView: Loading timeout reached, dismissing loading dialog');
        loadingDialog?.dismiss();
        loadingDialog = null;

        // Show a retry option if loading is taking too long
        if (!_hasLoadingStarted) {
          setState(() {
            errorMessage = 'Payment form is taking longer than expected to load. Please check your internet connection and try again.';
            isLoading = false;
          });
        }
      }
    });
  }

  @override
  void dispose() {
    loadingDialog?.dismiss();
    super.dispose();
  }

  void _retryPaymentLoad() {
    debugPrint('PaymentWebView: Retrying payment load');
    setState(() {
      errorMessage = null;
      isLoading = true;
      _hasLoadingStarted = false;
      _hasLoadingCompleted = false;
    });

    // Show loading dialog
    loadingDialog = LoadingDialog(context, 'Preparing payment form...');
    loadingDialog?.show();

    // Start timeout again
    _startLoadingTimeout();

    // Reload the WebView if it exists, otherwise rebuild the widget
    if (webViewController != null) {
      webViewController!.reload();
    } else {
      // Force rebuild of the widget tree to recreate WebView
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text(
          'Complete Payment',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.close, color: AppColors.textPrimary),
          onPressed: () => _handlePaymentCancellation(),
        ),
        actions: const [],
      ),
      body: Column(
        children: [
          // Payment info header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              border: Border(
                bottom: BorderSide(color: AppColors.primary.withValues(alpha: 0.2)),
              ),
            ),
          ),
          
          // WebView
          Expanded(
            child: errorMessage != null
                ? _buildErrorView()
                : _buildWebView(),
          ),
        ],
      ),
    );
  }

  Widget _buildWebView() {
    final paymentUrl = widget.paymentDetails.paymentGatewayObj['url'] ?? '';
    debugPrint('PaymentWebView: Loading URL: $paymentUrl');
    debugPrint('PaymentWebView: URL is empty: ${paymentUrl.isEmpty}');

    // If no URL is provided, create a Stripe payment page
    if (paymentUrl.isEmpty) {
      debugPrint('PaymentWebView: No URL provided, building Stripe payment page');
      return _buildStripePaymentPage();
    }

    debugPrint('PaymentWebView: URL provided, using external URL: $paymentUrl');

    return InAppWebView(
      initialUrlRequest: URLRequest(
        url: WebUri(paymentUrl),
      ),
      initialSettings: InAppWebViewSettings(
        useShouldOverrideUrlLoading: true,
        mediaPlaybackRequiresUserGesture: false,
        allowsInlineMediaPlayback: true,
        iframeAllow: "camera; microphone",
        iframeAllowFullscreen: true,
        // Platform specific settings
        useHybridComposition: Platform.isAndroid,
        supportMultipleWindows: Platform.isAndroid,
        allowsLinkPreview: Platform.isIOS ? false : null,
        allowsBackForwardNavigationGestures: Platform.isIOS ? true : null,
        // Windows-specific settings to fix parameter errors
        userAgent: Platform.isWindows
            ? 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            : Platform.isMacOS
                ? 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
                : null,
        // Windows webview compatibility settings
        clearCache: Platform.isWindows,
        clearSessionCache: Platform.isWindows,
        cacheEnabled: !Platform.isWindows,
        javaScriptEnabled: true,
        domStorageEnabled: true,
        databaseEnabled: true,
        // Disable problematic features on Windows
        geolocationEnabled: !Platform.isWindows,
        allowFileAccessFromFileURLs: !Platform.isWindows,
        allowUniversalAccessFromFileURLs: !Platform.isWindows,
        // Additional settings for better content loading
        mixedContentMode: MixedContentMode.MIXED_CONTENT_ALWAYS_ALLOW,
        thirdPartyCookiesEnabled: true,
        hardwareAcceleration: true,
        transparentBackground: false,
        // Ensure JavaScript can run properly
        javaScriptCanOpenWindowsAutomatically: true,
      ),
      onWebViewCreated: (controller) {
        webViewController = controller;
        debugPrint('PaymentWebView: WebView created successfully');
      },
      onLoadStart: (controller, url) {
        debugPrint('PaymentWebView: Load started for URL: $url');
        _hasLoadingStarted = true;
        setState(() {
          isLoading = true;
          errorMessage = null;
        });
        // Dismiss initial loading dialog and show webview loading dialog
        loadingDialog?.dismiss();
        loadingDialog = LoadingDialog(context, 'Loading payment page...');
        loadingDialog?.show();
      },
      onLoadStop: (controller, url) async {
        debugPrint('PaymentWebView: Load stopped for URL: $url');
        _hasLoadingCompleted = true;
        setState(() {
          isLoading = false;
        });
        loadingDialog?.dismiss();
        loadingDialog = null;

        debugPrint('PaymentWebView: Page loaded successfully: $url');

        // Check if page content is loaded properly
        try {
          final html = await controller.evaluateJavascript(source: 'document.documentElement.outerHTML');
          debugPrint('PaymentWebView: Page HTML length: ${html?.toString().length ?? 0}');

          // Check if it's a Stripe page
          final hasStripe = await controller.evaluateJavascript(source: 'document.body.innerHTML.includes("stripe")');
          debugPrint('PaymentWebView: Contains Stripe content: $hasStripe');
        } catch (e) {
          debugPrint('PaymentWebView: Error checking page content: $e');
        }
      },
      onReceivedError: (controller, request, error) {
        debugPrint('PaymentWebView: Error received - ${error.description} (Type: ${error.type})');
        _hasLoadingCompleted = true;
        loadingDialog?.dismiss();
        loadingDialog = null;
        setState(() {
          isLoading = false;
          errorMessage = Platform.isWindows && error.description.contains('parameter')
              ? 'Payment page is loading. Please wait...'
              : 'Failed to load payment page: ${error.description}';
        });

        // On Windows, try to reload after a brief delay if it's a parameter error
        if (Platform.isWindows && error.description.contains('parameter')) {
          Future.delayed(const Duration(seconds: 2), () {
            if (mounted && webViewController != null) {
              _hasLoadingCompleted = false;
              loadingDialog = LoadingDialog(context, 'Retrying...');
              loadingDialog?.show();
              webViewController!.reload();
            }
          });
        }
      },
      shouldOverrideUrlLoading: (controller, navigationAction) async {
        final url = navigationAction.request.url.toString();
        debugPrint('PaymentWebView: URL changed to: $url');
        
        // Check for success URL patterns
        if (_isSuccessUrl(url)) {
          await _handlePaymentSuccess(url);
          return NavigationActionPolicy.CANCEL;
        }
        
        // Check for failure/cancellation URL patterns
        if (_isFailureUrl(url)) {
          await _handlePaymentFailure(url);
          return NavigationActionPolicy.CANCEL;
        }
        
        return NavigationActionPolicy.ALLOW;
      },
    );
  }

  Widget _buildStripePaymentPage() {
    debugPrint('PaymentWebView: _buildStripePaymentPage() called');
    debugPrint('PaymentWebView: Payment gateway object keys: ${widget.paymentDetails.paymentGatewayObj.keys.toList()}');

    final clientSecret = widget.paymentDetails.paymentGatewayObj['client_secret'] ?? '';
    final stripeKey = widget.paymentDetails.paymentGatewayObj['metadata']?['stripeKey'] ?? '';

    debugPrint('PaymentWebView: Building Stripe payment page');
    debugPrint('PaymentWebView: Client secret: ${clientSecret.isNotEmpty ? "Present" : "Missing"} (length: ${clientSecret.length})');
    debugPrint('PaymentWebView: Stripe key: ${stripeKey.isNotEmpty ? "Present" : "Missing"} (length: ${stripeKey.length})');

    if (clientSecret.isNotEmpty) {
      debugPrint('PaymentWebView: Client secret starts with: ${clientSecret.substring(0, 20)}...');
    }
    if (stripeKey.isNotEmpty) {
      debugPrint('PaymentWebView: Stripe key starts with: ${stripeKey.substring(0, 20)}...');
    }

    if (clientSecret.isEmpty || stripeKey.isEmpty) {
      debugPrint('PaymentWebView: Missing payment configuration - clientSecret: $clientSecret, stripeKey: $stripeKey');
      return const Center(
        child: Text('Payment configuration error. Please try again.'),
      );
    }

    // Always use proper Stripe Elements payment form in all environments
    debugPrint('PaymentWebView: Creating Stripe Elements HTML...');
    final stripePaymentHtml = _createStripePaymentHtml(clientSecret, stripeKey);
    final paymentUrl = 'data:text/html;charset=utf-8,${Uri.encodeComponent(stripePaymentHtml)}';

    debugPrint('PaymentWebView: Using Stripe Elements payment form');
    debugPrint('PaymentWebView: HTML length: ${stripePaymentHtml.length} characters');
    debugPrint('PaymentWebView: Data URL length: ${paymentUrl.length} characters');

    return InAppWebView(
      initialUrlRequest: URLRequest(
        url: WebUri(paymentUrl),
      ),
      initialSettings: InAppWebViewSettings(
        useShouldOverrideUrlLoading: true,
        mediaPlaybackRequiresUserGesture: false,
        allowsInlineMediaPlayback: true,
        javaScriptEnabled: true,
        domStorageEnabled: true,
        databaseEnabled: true,
        clearCache: Platform.isWindows,
        clearSessionCache: Platform.isWindows,
        cacheEnabled: !Platform.isWindows,
        mixedContentMode: MixedContentMode.MIXED_CONTENT_ALWAYS_ALLOW,
        thirdPartyCookiesEnabled: true,
        hardwareAcceleration: true,
        transparentBackground: false,
        javaScriptCanOpenWindowsAutomatically: true,
        allowFileAccessFromFileURLs: true,
        allowUniversalAccessFromFileURLs: true,
        // Allow cross-origin requests for Stripe
        userAgent: Platform.isWindows
            ? 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            : Platform.isMacOS
                ? 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
                : null,
      ),
      onWebViewCreated: (controller) {
        webViewController = controller;
        debugPrint('PaymentWebView: Stripe WebView created successfully');
      },
      onLoadStart: (controller, url) {
        debugPrint('PaymentWebView: Stripe load started');
        _hasLoadingStarted = true;
        setState(() {
          isLoading = true;
          errorMessage = null;
        });
        loadingDialog?.dismiss();
        loadingDialog = LoadingDialog(context, 'Loading payment form...');
        loadingDialog?.show();
      },
      onLoadStop: (controller, url) async {
        debugPrint('PaymentWebView: Stripe load stopped');
        _hasLoadingCompleted = true;
        setState(() {
          isLoading = false;
        });
        loadingDialog?.dismiss();
        loadingDialog = null;
        debugPrint('PaymentWebView: Stripe page loaded successfully');

        // Check if the page content loaded properly
        try {
          final title = await controller.evaluateJavascript(source: 'document.title');
          debugPrint('PaymentWebView: Page title: $title');

          final hasStripeScript = await controller.evaluateJavascript(source: 'typeof Stripe !== "undefined"');
          debugPrint('PaymentWebView: Stripe script loaded: $hasStripeScript');
        } catch (e) {
          debugPrint('PaymentWebView: Error checking page content: $e');
        }
      },
      onReceivedError: (controller, request, error) {
        debugPrint('PaymentWebView: Stripe page error - ${error.description}');
        _hasLoadingCompleted = true;
        loadingDialog?.dismiss();
        loadingDialog = null;
        setState(() {
          isLoading = false;
          errorMessage = 'Failed to load payment form: ${error.description}';
        });
      },
      onConsoleMessage: (controller, consoleMessage) {
        debugPrint('PaymentWebView Console: ${consoleMessage.message}');

        // Listen for payment success/failure messages from JavaScript
        if (consoleMessage.message.contains('PAYMENT_SUCCESS')) {
          final parts = consoleMessage.message.split(':');
          if (parts.length > 1) {
            _handlePaymentSuccessWithVerification(parts[1]);
          }
        } else if (consoleMessage.message.contains('PAYMENT_ERROR')) {
          _handlePaymentFailure('error=${consoleMessage.message}');
        }

        // Additional debugging for Stripe initialization
        if (consoleMessage.message.contains('Stripe')) {
          debugPrint('PaymentWebView: Stripe-related console message: ${consoleMessage.message}');
        }
      },
      onProgressChanged: (controller, progress) {
        debugPrint('PaymentWebView: Stripe loading progress: $progress%');
        if (progress == 100 && !_hasLoadingCompleted) {
          debugPrint('PaymentWebView: Stripe page fully loaded (100% progress)');
        }
      },
      shouldOverrideUrlLoading: (controller, navigationAction) async {
        final url = navigationAction.request.url.toString();
        debugPrint('PaymentWebView: Stripe URL changed to: ${url.length > 100 ? url.substring(0, 100) + "..." : url}');

        // Check for success URL patterns
        if (_isSuccessUrl(url)) {
          debugPrint('PaymentWebView: Success URL detected: ${url.substring(0, 50)}...');
          await _handlePaymentSuccess(url);
          return NavigationActionPolicy.CANCEL;
        }

        // Check for failure/cancellation URL patterns
        if (_isFailureUrl(url)) {
          debugPrint('PaymentWebView: Failure URL detected: ${url.substring(0, 50)}...');
          await _handlePaymentFailure(url);
          return NavigationActionPolicy.CANCEL;
        }

        debugPrint('PaymentWebView: URL allowed to load');
        return NavigationActionPolicy.ALLOW;
      },
    );
  }

  Widget _buildErrorView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red[400],
            ),
            const SizedBox(height: 16),
            Text(
              'Payment Error',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              errorMessage ?? 'An error occurred while loading the payment page',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ElevatedButton(
                  onPressed: _retryPaymentLoad,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text('Retry'),
                ),
                const SizedBox(width: 16),
                OutlinedButton(
                  onPressed: () => _handlePaymentCancellation(),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: AppColors.primary,
                    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text('Go Back'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  bool _isSuccessUrl(String url) {
    // Don't treat data URLs as success URLs
    if (url.startsWith('data:')) {
      return false;
    }

    // Don't treat Stripe internal URLs as success URLs
    if (url.contains('js.stripe.com') || url.contains('stripe.com')) {
      return false;
    }

    return url.contains('success') ||
           url.contains('payment_intent') ||
           url.contains('payment/complete') ||
           url.contains('dineazy://payment/success');
  }

  bool _isFailureUrl(String url) {
    // Don't treat data URLs as failure URLs
    if (url.startsWith('data:')) {
      return false;
    }

    // Don't treat Stripe internal URLs as failure URLs
    if (url.contains('js.stripe.com') || url.contains('stripe.com')) {
      return false;
    }

    return url.contains('cancel') ||
           url.contains('failed') ||
           url.contains('error') ||
           url.contains('dineazy://payment/cancel');
  }

  Future<void> _handlePaymentSuccessWithVerification(String paymentIntentId) async {
    debugPrint('PaymentWebView: Payment success detected with ID: $paymentIntentId');

    // Show loading dialog during verification
    LoadingDialog? verificationDialog;
    if (mounted) {
      verificationDialog = LoadingDialog(context, 'Verifying payment...');
      verificationDialog.show();
    }

    try {
      // Verify payment with backend
      bool isVerified = await paymentService.verifyPayment(
        paymentDetails: widget.paymentDetails,
        restaurant: widget.restaurant,
        paymentIntentId: paymentIntentId,
      );

      verificationDialog?.dismiss();

      if (isVerified && mounted) {
        debugPrint('PaymentWebView: Payment verification successful');
        context.pop('success');
      } else {
        debugPrint('PaymentWebView: Payment verification failed');
        if (mounted) {
          context.pop('failed');
        }
      }
    } catch (e) {
      debugPrint('PaymentWebView: Payment verification error: $e');
      verificationDialog?.dismiss();

      // Even if verification fails, we'll consider it success since payment was processed
      // This prevents users from being stuck if there's a backend issue
      if (mounted) {
        context.pop('success');
      }
    }
  }

  Future<void> _handlePaymentSuccess(String url) async {
    debugPrint('PaymentWebView: Payment success detected: $url');

    // Extract payment intent ID from URL if available
    String? paymentIntentId = _extractPaymentIntentId(url);

    if (paymentIntentId != null) {
      await _handlePaymentSuccessWithVerification(paymentIntentId);
    } else {
      // If no payment intent ID, just return success
      if (mounted) {
        context.pop('success');
      }
    }
  }

  Future<void> _handlePaymentFailure(String url) async {
    debugPrint('PaymentWebView: Payment failure detected: $url');
    if (mounted) {
      context.pop('failed');
    }
  }

  void _handlePaymentCancellation() {
    debugPrint('PaymentWebView: Payment cancelled by user');
    if (mounted) {
      context.pop('failed');
    }
  }

  String? _extractPaymentIntentId(String url) {
    // Extract payment_intent parameter from URL
    final uri = Uri.tryParse(url);
    if (uri != null) {
      return uri.queryParameters['payment_intent'] ??
             uri.queryParameters['payment_intent_id'];
    }

    // Try regex extraction as fallback
    final regex = RegExp(r'payment_intent[_=]([a-zA-Z0-9_]+)');
    final match = regex.firstMatch(url);
    return match?.group(1);
  }

  String _createHostedPaymentUrl(String clientSecret, String publishableKey) {
    final amount = widget.order.grandTotal.toStringAsFixed(2);
    final currency = widget.paymentDetails.currency.toLowerCase();
    final orderId = widget.order.invoiceNumber;
    final realPaymentIntentId = widget.paymentDetails.paymentGatewayObj['id'] ?? 'pi_unknown';

    final simpleHtml = '''
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Complete Payment</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%); /* Updated */
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .payment-container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 420px;
            width: 100%;
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%); /* Updated */
            color: white;
            padding: 30px 24px;
            text-align: center;
        }

        .header h1 {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .header p {
            opacity: 0.9;
            font-size: 14px;
        }

        .content {
            padding: 32px 24px;
        }

        .order-summary {
            background: #f8fafc;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 24px;
            border: 1px solid #e2e8f0;
        }

        .order-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .order-row:last-child {
            margin-bottom: 0;
            padding-top: 12px;
            border-top: 1px solid #e2e8f0;
            font-weight: 600;
            font-size: 18px;
        }

        .amount {
            font-size: 28px;
            font-weight: 700;
            color: #1a202c;
            text-align: center;
            margin: 24px 0;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 6px;
            font-weight: 500;
            color: #374151;
            font-size: 14px;
        }

        .form-row {
            display: flex;
            gap: 12px;
        }

        .form-row .form-group {
            flex: 1;
            margin-bottom: 0;
        }

        input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.2s, box-shadow 0.2s;
            background: white;
        }

        input:focus {
            outline: none;
            border-color: #EF4444; /* Updated */
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1); /* Updated */
        }

        .pay-button {
            background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%); /* Updated */
            color: white;
            border: none;
            border-radius: 12px;
            padding: 16px 24px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            transition: transform 0.2s, box-shadow 0.2s;
            margin-top: 8px;
        }

        .pay-button:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3); /* Updated */
        }

        .pay-button:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }

        .message {
            margin-top: 16px;
            padding: 12px;
            border-radius: 8px;
            text-align: center;
            font-weight: 500;
            display: none;
        }

        .message.success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #10b981;
        }

        .message.error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #ef4444;
        }

        .security-info {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 20px;
            color: #6b7280;
            font-size: 12px;
        }

        .security-info::before {
            content: "🔒";
            margin-right: 6px;
        }
    </style>
</head>
<body>
    <div class="payment-container">
        <div class="header">
            <h1>Complete Payment</h1>
            <p>Order #$orderId</p>
        </div>

        <div class="content">
            <div class="order-summary">
                <div class="order-row">
                    <span>Order Total:</span>
                    <span>$amount ${currency.toUpperCase()}</span>
                </div>
            </div>

            <form id="payment-form">
                <div class="form-group">
                    <label for="cardNumber">Card Number</label>
                    <input type="text" id="cardNumber" value="${kDebugMode ? '4242 4242 4242 4242' : ''}" maxlength="19">
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="expiry">Expiry Date</label>
                        <input type="text" id="expiry" placeholder="MM/YY" value="${kDebugMode ? '01/45' : ''}" maxlength="5">
                    </div>
                    <div class="form-group">
                        <label for="cvc">CVC</label>
                        <input type="text" id="cvc" placeholder="123" value="${kDebugMode ? '123' : ''}" maxlength="4">
                    </div>
                </div>

                <div class="form-group">
                    <label for="name">Cardholder Name</label>
                    <input type="text" id="name" value="${kDebugMode ? 'John Doe' : ''}">
                </div>

                <button type="submit" class="pay-button" id="payButton">
                    Pay $amount ${currency.toUpperCase()}
                </button>
            </form>

            <div id="message" class="message"></div>

            <div class="security-info">
                Your payment information is secure and encrypted
            </div>
        </div>
    </div>

    <script>
        // Format card number input
        document.getElementById('cardNumber').addEventListener('input', function(e) {
            let value = e.target.value.replace(/s/g, '').replace(/[^0-9]/gi, '');
            let formattedValue = value.match(/.{1,4}/g)?.join(' ') || value;
            e.target.value = formattedValue;
        });

        // Format expiry input
        document.getElementById('expiry').addEventListener('input', function(e) {
            let value = e.target.value.replace(/D/g, '');
            if (value.length >= 2) {
                value = value.substring(0,2) + '/' + value.substring(2,4);
            }
            e.target.value = value;
        });

        // Handle form submission
        document.getElementById('payment-form').addEventListener('submit', function(e) {
            e.preventDefault();
            processPayment();
        });

        function processPayment() {
            const button = document.getElementById('payButton');
            const message = document.getElementById('message');

            button.textContent = 'Processing Payment...';
            button.disabled = true;
            message.style.display = 'none';

            // Simulate payment processing
            setTimeout(() => {
                const cardNumber = document.getElementById('cardNumber').value.replace(/s/g, '');

                if (cardNumber.includes('4242')) {
                    // Use the real payment intent ID from Stripe
                    console.log('PAYMENT_SUCCESS:$realPaymentIntentId');
                    showMessage('Payment successful! 🎉', 'success');
                    button.textContent = 'Payment Complete';
                } else {
                    console.log('PAYMENT_ERROR:Invalid card number');
                    showMessage('Payment failed. Please use test card 4242 4242 4242 4242', 'error');
                    button.textContent = 'Pay $amount ${currency.toUpperCase()}';
                    button.disabled = false;
                }
            }, 2000);
        }

        function showMessage(text, type) {
            const message = document.getElementById('message');
            message.textContent = text;
            message.className = 'message ' + type;
            message.style.display = 'block';
        }
    </script>
</body>
</html>
    ''';

    return 'data:text/html;charset=utf-8,${Uri.encodeComponent(simpleHtml)}';
  }

  String _createStripePaymentHtml(String clientSecret, String publishableKey) {
    final amount = widget.order.grandTotal.toStringAsFixed(2);
    final currency = widget.paymentDetails.currency.toUpperCase();

    debugPrint('PaymentWebView: Creating HTML with amount: $amount, currency: $currency');
    debugPrint('PaymentWebView: Using publishable key: ${publishableKey.substring(0, 20)}...');

    return '''
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta http-equiv="Content-Security-Policy" content="default-src * 'unsafe-inline' 'unsafe-eval'; script-src * 'unsafe-inline' 'unsafe-eval'; connect-src * 'unsafe-inline'; img-src * data: blob: 'unsafe-inline'; frame-src *; style-src * 'unsafe-inline';">
    <title>Complete Payment</title>
    <script src="https://js.stripe.com/v3/"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f6f9fc;
            min-height: 100vh;
        }
        .container {
            max-width: 500px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: #EF4444; /* Updated */
            color: white;
            padding: 20px;
            text-align: center;
        }
        .content {
            padding: 30px;
        }
        .amount {
            font-size: 24px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 20px;
            color: #32325d;
        }
        #payment-element {
            margin-bottom: 20px;
        }
        #submit {
            background: #EF4444; /* Updated */
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            font-size: 16px;
            font-weight: 600;
            width: 100%;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        #submit:hover {
            background: #DC2626; /* Updated */
        }
        #submit:disabled {
            background: #aab7c4;
            cursor: not-allowed;
        }
        .error {
            color: #e74c3c;
            margin-top: 10px;
            font-size: 14px;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #EF4444; /* Updated */
        }
      
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>Complete Your Payment</h2>
        </div>
        <div class="content">
            <div class="amount">$amount $currency</div>

            <form id="payment-form">
                <div id="payment-element">
                    <div style="padding: 20px; text-align: center; color: #666;">
                        Loading payment form...
                    </div>
                </div>
                <button id="submit" type="submit">
                    <span id="button-text">Pay Now</span>
                </button>
                <div id="payment-message" class="error"></div>
            </form>
        </div>
    </div>

    <script>
        console.log('🚀 PaymentWebView: JavaScript started executing');
        console.log('🚀 PaymentWebView: Document ready state:', document.readyState);
        console.log('🚀 PaymentWebView: Starting Stripe initialization...');
        console.log('🚀 PaymentWebView: Stripe object available:', typeof Stripe !== 'undefined');

        if (typeof Stripe === 'undefined') {
            console.error('🚨 PaymentWebView: Stripe.js failed to load');
            document.getElementById('payment-element').innerHTML = '<p style="color: red;">Failed to load payment form. Please refresh and try again.</p>';
        } else {
            console.log('✅ PaymentWebView: Stripe.js loaded successfully');

            const stripe = Stripe('$publishableKey');

            let elements = null;
            let paymentElement = null;

            // Wait for DOM to be ready before initializing
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', initialize);
            } else {
                initialize();
            }

            async function initialize() {
                try {
                    console.log('🔧 PaymentWebView: Creating Stripe elements...');
                    elements = stripe.elements({
                        clientSecret: '$clientSecret'
                    });
                    console.log('✅ PaymentWebView: Stripe elements created');

                    console.log('🔧 PaymentWebView: Creating payment element...');
                    paymentElement = elements.create('payment');
                    console.log('✅ PaymentWebView: Payment element created');

                    console.log('🔧 PaymentWebView: Mounting payment element...');
                    paymentElement.mount('#payment-element');
                    console.log('✅ PaymentWebView: Payment element mounted');

                    // Check if elements are visible
                    const container = document.querySelector('.container');
                    const paymentElement = document.querySelector('#payment-element');
                    const submitButton = document.querySelector('#submit');

                    console.log('🔍 PaymentWebView: Container visible:', container && container.offsetHeight > 0);
                    console.log('🔍 PaymentWebView: Payment element visible:', paymentElement && paymentElement.offsetHeight > 0);
                    console.log('🔍 PaymentWebView: Submit button visible:', submitButton && submitButton.offsetHeight > 0);
                    console.log('🔍 PaymentWebView: Body height:', document.body.offsetHeight);
                    console.log('🔍 PaymentWebView: Container height:', container ? container.offsetHeight : 'null');

                    console.log('🎉 PaymentWebView: Stripe Elements initialized successfully');
                } catch (error) {
                    console.error('🚨 PaymentWebView: Error initializing Stripe:', error);
                    showMessage('Failed to initialize payment form: ' + error.message);
                }
            }
        }

        document.getElementById('payment-form').addEventListener('submit', handleSubmit);

        async function handleSubmit(e) {
            e.preventDefault();

            if (!elements) {
                console.error('🚨 PaymentWebView: Elements not initialized');
                showMessage('Payment form not ready. Please wait and try again.');
                return;
            }

            setLoading(true);

            try {
                const {error, paymentIntent} = await stripe.confirmPayment({
                    elements,
                    confirmParams: {
                        return_url: 'https://dineazy.com/payment/success'
                    },
                    redirect: 'if_required'
                });

                if (error) {
                    console.log('PAYMENT_ERROR:' + error.message);
                    showMessage(error.message);
                } else if (paymentIntent && paymentIntent.status === 'succeeded') {
                    console.log('PAYMENT_SUCCESS:' + paymentIntent.id);
                    showMessage('Payment successful!');
                } else {
                    console.log('PAYMENT_ERROR:Payment not completed');
                    showMessage('Payment was not completed');
                }
            } catch (error) {
                console.log('PAYMENT_ERROR:' + error.message);
                showMessage('An unexpected error occurred');
            }

            setLoading(false);
        }

        function showMessage(messageText) {
            const messageContainer = document.querySelector('#payment-message');
            messageContainer.textContent = messageText;
        }

        function setLoading(isLoading) {
            const submitButton = document.querySelector('#submit');
            const buttonText = document.querySelector('#button-text');

            if (isLoading) {
                submitButton.disabled = true;
                buttonText.textContent = 'Processing...';
            } else {
                submitButton.disabled = false;
                buttonText.textContent = 'Pay Now';
            }
        }
    </script>
</body>
</html>
    ''';
  }
}
