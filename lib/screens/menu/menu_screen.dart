import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:provider/provider.dart';

import '../../common/constants/colors.dart';
import '../../models/food/food_sub_category.dart';
import '../../models/order/order_type.dart';
import '../../providers/cart_provider.dart';
import '../../providers/data_provider.dart';
import '../../widgets/cart_bottom_bar.dart';
import '../../widgets/loading_dialog.dart';
import 'widgets/item_card.dart';

class MenuScreen extends StatefulWidget {
  const MenuScreen({super.key});

  @override
  State<MenuScreen> createState() => _MenuScreenState();
}

class _MenuScreenState extends State<MenuScreen> with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;
  late AnimationController _cartBarController;
  late Animation<double> _cartBarAnimation;
  FoodSubCategory? selectedCategory;

  final ScrollController _scrollController = ScrollController();
  bool _isCartBarVisible = true;
  double _lastScrollOffset = 0;

  @override
  void initState() {
    super.initState();

    _fadeController = AnimationController(duration: const Duration(milliseconds: 500), vsync: this);
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _fadeController, curve: Curves.easeOut));
    _fadeController.forward();

    _cartBarController = AnimationController(duration: const Duration(milliseconds: 300), vsync: this);
    _cartBarAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _cartBarController, curve: Curves.easeInOut));

    // Initialize cart bar as visible
    _cartBarController.forward();

    // Add scroll listener
    _scrollController.addListener(_onScroll);

    SchedulerBinding.instance.addPostFrameCallback((timeStamp) {
      final DataProvider dataProvider = Provider.of<DataProvider>(context, listen: false);
      final CartProvider cartProvider = Provider.of<CartProvider>(context, listen: false);
      final loadingDialog = LoadingDialog(context, 'Cataloguing menu...');
      loadingDialog.show();

      debugPrint('🐞 _MenuScreenState.initState: Loading menu...');
      dataProvider.loadMenu(dataProvider.restaurant, OrderType.takeaway).then((_) {
        loadingDialog.dismiss();
        if (dataProvider.hasMenu) {
          cartProvider.setDataProvider(dataProvider);
          setState(() {
            selectedCategory = dataProvider.menu.nonEmptySubCategories.firstOrNull;
          });
        } else {
          debugPrint('❌ _MenuScreenState.initState: Failed to load menu');
        }
      }).catchError((error) {
        loadingDialog.dismiss();
        debugPrint('❌ _MenuScreenState.initState: Error loading menu: $error');
      });
    });
  }

  void _onScroll() {
    final currentOffset = _scrollController.offset;
    final delta = currentOffset - _lastScrollOffset;

    if (delta.abs() > 5) {
      final shouldShowCartBar = delta < 0;

      if (shouldShowCartBar != _isCartBarVisible) {
        setState(() {
          _isCartBarVisible = shouldShowCartBar;
        });

        if (_isCartBarVisible) {
          _cartBarController.forward();
        } else {
          _cartBarController.reverse();
        }
      }
    }

    _lastScrollOffset = currentOffset;
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _cartBarController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final DataProvider dataProvider = Provider.of<DataProvider>(context);

    final menuItems = dataProvider.menu;

    final subCategories = dataProvider.menu.nonEmptySubCategories;

    final filteredMenuItems = selectedCategory == null
        ? menuItems.allFoodItems
        : menuItems.getItemsOfSubCategory(selectedCategory!.id);

    return Scaffold(
      backgroundColor: AppColors.background,
      body: Stack(
        children: [
          FadeTransition(
            opacity: _fadeAnimation,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: const EdgeInsets.fromLTRB(32, 8, 32, 24),
                  decoration: BoxDecoration(gradient: AppColors.cardGradient, boxShadow: AppColors.elevatedShadow),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: AppColors.secondary.withAlpha(1),
                              borderRadius: BorderRadius.circular(16),
                            ),
                            child: Icon(Icons.restaurant_menu, color: AppColors.secondary, size: 28),
                          ),
                          const SizedBox(width: 16),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'Our Menu',
                                style: TextStyle(
                                  fontSize: 32,
                                  fontWeight: FontWeight.w800,
                                  color: AppColors.textPrimary,
                                  letterSpacing: -1,
                                ),
                              ),
                              const Text(
                                'Touch to add items to your order',
                                style: TextStyle(
                                  fontSize: 16,
                                  color: AppColors.textSecondary,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                      const SizedBox(height: 14),

                      // Category Filter
                      SizedBox(
                        height: 50,
                        child: ListView.builder(
                          scrollDirection: Axis.horizontal,
                          itemCount: subCategories.length,
                          itemBuilder: (context, index) {
                            final subCategory = subCategories.elementAt(index);
                            final isSelected = subCategory == selectedCategory;

                            return Padding(
                              padding: EdgeInsets.only(left: 24),
                              child: AnimatedContainer(
                                duration: const Duration(milliseconds: 200),
                                child: FilterChip(
                                  label: Text(
                                    subCategory.name,
                                    style: TextStyle(
                                      color: isSelected ? AppColors.textOnDark : AppColors.textSecondary,
                                      fontWeight: FontWeight.w600,
                                      fontSize: 18,
                                    ),
                                  ),
                                  selected: isSelected,
                                  onSelected: (selected) {
                                    setState(() => selectedCategory = subCategory);
                                  },
                                  backgroundColor: AppColors.filterUnselected,
                                  selectedColor: AppColors.primary,
                                  checkmarkColor: AppColors.textOnDark,
                                  elevation: isSelected ? 4 : 1,
                                  shadowColor: AppColors.filterSelected.withValues(alpha: 0.3),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(25),
                                    side: BorderSide(
                                      color: isSelected ? AppColors.filterSelected : AppColors.filterBorder,
                                    ),
                                  ),
                                  padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),

                Expanded(
                  child: Container(
                    margin: const EdgeInsets.only(left: 30, right: 30, bottom: 20, top: 20),
                    // Fixed bottom margin for cart space
                    child: filteredMenuItems.isEmpty
                        ? Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(Icons.search_off, size: 64, color: AppColors.textTertiary),
                                const SizedBox(height: 16),
                                const Text(
                                  'No items in this category',
                                  style: TextStyle(
                                    fontSize: 18,
                                    color: AppColors.textSecondary,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          )
                        : GridView.builder(
                            controller: _scrollController,
                            physics: const BouncingScrollPhysics(),
                            gridDelegate: const SliverGridDelegateWithMaxCrossAxisExtent(
                              maxCrossAxisExtent: 400,
                              crossAxisSpacing: 24,
                              mainAxisSpacing: 24,
                              childAspectRatio: 0.78,
                            ),
                            itemCount: filteredMenuItems.length,
                            itemBuilder: (context, index) {
                              final item = filteredMenuItems.elementAt(index);

                              return AnimatedContainer(
                                duration: Duration(milliseconds: 200 + (index * 50)),
                                curve: Curves.easeOutBack,
                                child: ItemCard(
                                  item: item,
                                  // addOnItems: addOnItems,
                                ),
                              );
                            },
                          ),
                  ),
                ),
              ],
            ),
          ),
          AnimatedBuilder(
            animation: _cartBarAnimation,
            builder: (context, child) {
              return Positioned(
                bottom:
                    MediaQuery.of(context).size.height * 0.005 -
                    (160 * (1 - _cartBarAnimation.value)), // Slide down when hidden
                left: MediaQuery.of(context).size.width * 0.2,
                right: MediaQuery.of(context).size.width * 0.2,
                child: Opacity(
                  opacity: _cartBarAnimation.value,
                  child: Center(child: CartBottomBar()),
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}
